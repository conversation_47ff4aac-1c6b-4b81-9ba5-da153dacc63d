package com.yunqu.yc.govphonemgmt.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.govphonemgmt.base.AppBaseServlet;
import com.yunqu.yc.govphonemgmt.base.CommonLogger;
import com.yunqu.yc.govphonemgmt.base.Constants;
import com.yunqu.yc.govphonemgmt.dao.sql.CallStatSql;
import com.yunqu.yc.govphonemgmt.util.ExcelUtil;
import com.yunqu.yc.govphonemgmt.util.LogUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 报表数据导出
 */
@WebServlet("/servlet/callStat")
public class CallStatServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	protected Logger logger = CommonLogger.logger;

	public EasyQuery getQuery() {
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
		query.setMaxRow(20000);
		return query;
	}


	/**
	 * 呼入话务量报表
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-callin"},  msg="没有权限")
	public void actionForExportCallInStat() {
		try {
			HttpServletRequest request = this.getRequest();
			if (isNullVerify(request)) {
				renderJson(EasyResult.fail("开始结束时间不能为空"));
				return;
			}
			JSONObject param = ExcelUtil.requestToJsonObject(getRequest());

			EasySQL sql = CallStatSql.getCallInStatSql(param);
			List<JSONObject> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			data = CallStatSql.sumCallInStat(data,true);
			JSONObject exParam = new JSONObject();
			exParam.putAll(param);
			exParam.put("data", data);
			LogUtils.log(LogUtils.MOUDLE_TYPE_STAT,"","",LogUtils.OPER_TYPE_EXPORT,
					"导出呼入话务量报表，导出时间范围："+param.getString("BEGIN_TIME")+"至"+param.getString("END_TIME")+",数据条数："+data.size()+"。",
					this.getUserPrincipal(),this.getRequest());
			commonExport2("template/template_callin_stat.xls", "呼入话务量报表", exParam, request,
					this.getResponse());
		} catch (Exception e) {
			e.printStackTrace();
			this.error(e.getMessage(), e);
		}
	}

	/**
	 * 呼出话务量报表
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-callout"},  msg="没有权限")
	public void actionForExportCallOutStat() {
		try {
			HttpServletRequest request = this.getRequest();
			if (isNullVerify(request)) {
				renderJson(EasyResult.fail("开始结束时间不能为空"));
				return;
			}
			JSONObject param = ExcelUtil.requestToJsonObject(getRequest());

			EasySQL sql = CallStatSql.getCallOutStatSql(param);
			List<JSONObject> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			data = CallStatSql.sumCallOutStat(data,true);
			JSONObject exParam = new JSONObject();
			exParam.putAll(param);
			exParam.put("data", data);
			LogUtils.log(LogUtils.MOUDLE_TYPE_STAT,"","",LogUtils.OPER_TYPE_EXPORT,
					"导出呼出话务量报表，导出时间范围："+param.getString("BEGIN_TIME")+"至"+param.getString("END_TIME")+",数据条数："+data.size()+"。",
					this.getUserPrincipal(),this.getRequest());
					commonExport2("template/template_callout_stat.xls", "呼出话务量报表", exParam, request,
					this.getResponse());
		} catch (Exception e) {
			e.printStackTrace();
			this.error(e.getMessage(), e);
		}
	}

	/**
	 * 满意度分析报表
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-satisfaction"},  msg="没有权限")
	public void actionForExportSatisfactionStat() {
		try {
			HttpServletRequest request = this.getRequest();
			if (isNullVerify(request)) {
				renderJson(EasyResult.fail("开始结束时间不能为空"));
				return;
			}
			JSONObject param = ExcelUtil.requestToJsonObject(getRequest());

			EasySQL sql = CallStatSql.getSatisfactionStatSql(param);
			List<JSONObject> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			data = CallStatSql.sumSatisfactionStat(data,true);
			JSONObject exParam = new JSONObject();
			exParam.putAll(param);
			exParam.put("data", data);
			LogUtils.log(LogUtils.MOUDLE_TYPE_STAT,"","",LogUtils.OPER_TYPE_EXPORT,
					"导出满意度分析报表，导出时间范围："+param.getString("BEGIN_TIME")+"至"+param.getString("END_TIME")+",数据条数："+data.size()+"。",
					this.getUserPrincipal(),this.getRequest());
			commonExport2("template/template_satisfaction_stat.xls", "满意度分析报表", exParam, request,
					this.getResponse());
		} catch (Exception e) {
			e.printStackTrace();
			this.error(e.getMessage(), e);
		}
	}

	// 格式化为百分比
	public String formatRate(String value) {
		if (StringUtils.isNotBlank(value)) {
			try {

				BigDecimal a = new BigDecimal(value);
				BigDecimal b = new BigDecimal("100");
				return a.multiply(b).doubleValue() + "%";
			} catch (Exception e) {
				return "0%";
			}

		} else {
			return "0%";
		}
	}

	/**
	 * 非空验证
	 */
	public boolean isNullVerify(HttpServletRequest request) {
		String beginTime = request.getParameter("BEGIN_TIME");
		String endTime = request.getParameter("END_TIME");
		return StringUtils.isBlank(beginTime) || StringUtils.isBlank(endTime);
	}


	public static List<Map<String, String>> toMapList(List<JSONObject> jsonList) {
		List<Map<String, String>> resultMapList = new ArrayList<>();

		for (JSONObject jsonObject : jsonList) {
			Map<String, String> map = new HashMap<>();

			for (String key : jsonObject.keySet()) {
				String value = jsonObject.getString(key);
				map.put(key, value);
			}

			resultMapList.add(map);
		}

		return resultMapList;
	}

	public void commonExport2(String path, String fileName, JSONObject exParam, HttpServletRequest request,
							  HttpServletResponse response) {
		String templatePath = request.getSession().getServletContext().getRealPath("/") + path;
		String date = EasyDate.getCurrentDateString("yyyy-MM-dd HH-mm-ss");
		ExcelUtil.getInstance().exportExcelFile(templatePath, fileName + "_" + date + ".xls", exParam, request,
				response);
	}






	@Override
	protected String getResId() {
		// TODO Auto-generated method stub
		return null;
	}
}
