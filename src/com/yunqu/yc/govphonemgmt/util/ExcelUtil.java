package com.yunqu.yc.govphonemgmt.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.yc.govphonemgmt.base.CommonLogger;
import net.sf.jxls.transformer.XLSTransformer;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

/**
 * 导出Excel公共方法
 */
public class ExcelUtil {

	private static volatile ExcelUtil excelUtil;
	

	private ExcelUtil() {

	}

	public static ExcelUtil getInstance() {
		if (null == excelUtil) {
			synchronized (ExcelUtil.class) {
				if (null == excelUtil) {
					excelUtil = new ExcelUtil();
				}
			}
		}
		return excelUtil;
	}

	public void exportObject2File(String templatePath, String targetPath, JSONObject param) {
		exportWorkbook(param, templatePath, targetPath);
	}

	public void exportExcelFile(String templatePath, String fileName, JSONObject param, HttpServletRequest req,
			HttpServletResponse resp) {
		File targetFile = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		if (exportWorkbook(param, templatePath, targetFile.getAbsolutePath())) {
			Render.renderFile(req, resp, targetFile, fileName);
		}
	}

	private boolean exportWorkbook(JSONObject param, String templatePath, String targetFilePath) {
		boolean result = false;
		try {
			if (StringUtils.isBlank(templatePath)) {
				CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " EXCEL模板文件路径为空，请检查！");
				return result;
			}
			File file = new File(templatePath);
			if (!file.exists()) {
				CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " EXCEL模板文件不存在，请检查！");
				return result;
			}
			if (file != null) {
				FileInputStream in = new FileInputStream(file);
				XLSTransformer xlsTransformer = new XLSTransformer();
				Workbook workbook = xlsTransformer.transformXLS(in, param);
				workbook.write(new FileOutputStream(targetFilePath));
				result = true;
			}
		} catch (Exception e) {
			CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " 导出Excel文件异常：" + e.getMessage(),
					e);
		}
		return result;
	}

	/**
	 * easyExcel 导出
	 */

	/**
	 * 按照模板导出（实体类）
	 * 
	 * @param header
	 *            模板标题
	 * @param data
	 *            模板数据
	 * @param fileName
	 *            导出文件名
	 * @param templatePath
	 *            模板路径
	 * @param req
	 * @param resp
	 */
	public void exportExcelForTemplate(Map<String, String> header, List<?> data, String fileName, String templatePath,
			HttpServletRequest req, HttpServletResponse resp) {
		ExcelWriter excelWriter = null;
		try (OutputStream out = resp.getOutputStream()) {
			if (StringUtils.isBlank(templatePath)) {
				CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " EXCEL模板文件路径为空，请检查！");
				return;
			}
			File file = new File(templatePath);
			if (!file.exists()) {
				CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " EXCEL模板文件不存在，请检查！");
				return;
			}
			resp.setContentType("application/vnd.ms-excel");
			resp.setCharacterEncoding("utf-8");
			resp.setHeader("Content-disposition", "attachment;" + Render.encodeFileName(req, fileName));
			excelWriter = EasyExcel.write(out).withTemplate(templatePath).build();
			WriteSheet writeSheet = EasyExcel.writerSheet().build();
			FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
			excelWriter.fill(data, fillConfig, writeSheet);
			excelWriter.fill(header, writeSheet);
			excelWriter.finish();
		} catch (IOException e) {
			CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " 导出Excel文件异常：" + e.getMessage(),
					e);
		} finally {
			if (excelWriter != null) {
				excelWriter.finish();
			}
		}

	}


	public void exportExcelForTemplate2(Map<String, String> param,List<?> headers, List<?> data, String fileName, String templatePath,
									   HttpServletRequest req, HttpServletResponse resp) {
		ExcelWriter excelWriter = null;
		try (OutputStream out = resp.getOutputStream()) {
			if (StringUtils.isBlank(templatePath)) {
				CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " EXCEL模板文件路径为空，请检查！");
				return;
			}
			File file = new File(templatePath);
			if (!file.exists()) {
				CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " EXCEL模板文件不存在，请检查！");
				return;
			}
			resp.setContentType("application/vnd.ms-excel");
			resp.setCharacterEncoding("utf-8");
			resp.setHeader("Content-disposition", "attachment;" + Render.encodeFileName(req, fileName));
			excelWriter = EasyExcel.write(out).withTemplate(templatePath).build();
			WriteSheet writeSheet = EasyExcel.writerSheet().build();
			FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();

			excelWriter.fill(new FillWrapper("headers", headers), fillConfig, writeSheet);
			excelWriter.fill(new FillWrapper("data", data), writeSheet);

			excelWriter.fill(param, writeSheet);

			excelWriter.finish();
		} catch (Exception e) {
			CommonLogger.getLogger().error(CommonUtil.getClassNameAndMethod(this) + " 导出Excel文件异常：" + e.getMessage(),
					e);
			e.printStackTrace();
		} finally {
			if (excelWriter != null) {
				excelWriter.finish();
			}
		}

	}

	public void saveExcelForTemplate(Map<String, String> header, List<?> data, String filePath, String fileDir,
			String templatePath) throws Exception {
		ExcelWriter excelWriter = null;
		try {
			if (StringUtils.isBlank(templatePath)) {
				throw new Exception(
						CommonUtil.getClassNameAndMethod(this) + " EXCEL模板文件(" + templatePath + ")不存在，请检查！");
			}
			File file = new File(templatePath);
			if (!file.exists()) {
				throw new Exception(
						CommonUtil.getClassNameAndMethod(this) + " EXCEL模板文件(" + templatePath + ")不存在，请检查！");
			}
			CommonLogger.getLogger().info("fileDir:"+fileDir+"&&fileName:"+filePath);
			if (!new File(fileDir).exists()) {
				new File(fileDir).mkdir();
			}
			File fileFill = new File(filePath);
			if (!fileFill.exists()) {
				fileFill.createNewFile();
			}
			excelWriter = EasyExcel.write(fileFill).withTemplate(templatePath).build();
			WriteSheet writeSheet = EasyExcel.writerSheet().build();
			FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
			excelWriter.fill(data, fillConfig, writeSheet);
			excelWriter.fill(header, writeSheet);
			excelWriter.finish();
		} catch (Exception e) {
			//e.printStackTrace();
			throw new Exception(e.getMessage());
		} finally {
			if (excelWriter != null) {
				excelWriter.finish();
			}
		}

	}




	/**
	 * 构建表头
	 * @param head
	 * @param name
	 * @return
	 */
	public static List<List<String>> formatHeader(List<String> head, String name) {
		List<List<String>> header = new ArrayList<List<String>>();
		for (String field : head) {
			List<String> list = new ArrayList<String>();
			list.add(name);
			list.add(name);
			list.add(field);
			header.add(list);
		}
		return header;
	}

	

	
	public static JSONObject requestToJsonObject(HttpServletRequest request) {
		JSONObject requestJson = new JSONObject();
		Enumeration<String> paramNames = request.getParameterNames();
		while (paramNames.hasMoreElements()) {
			String paramName = paramNames.nextElement();
			String[] pv = request.getParameterValues(paramName);
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < pv.length; i++) {
				if (pv[i].length() > 0) {
					if (i > 0) {
						sb.append(",");
					}
					sb.append(pv[i]);
				}
			}
			requestJson.put(paramName, sb.toString());
		}
		return requestJson;
	}
}