package com.yunqu.yc.govphonemgmt.util;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.govphonemgmt.base.CommonLogger;
import com.yunqu.yc.govphonemgmt.base.QueryFactory;
import com.yunqu.yc.govphonemgmt.model.UserModel;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;

/**
 * 操作流水
 * <AUTHOR>
 *
 */
public class LogUtils {

	public static final int MOUDLE_TYPE_LOGIN = 10;//登录认证
	public static final int MOUDLE_TYPE_SKILLGROUP = 20;//组织架构
	public static final int MOUDLE_TYPE_ROLE = 30;//角色管理
	public static final int MOUDLE_TYPE_USER = 40;//用户管理
	public static final int MOUDLE_TYPE_PHONE = 50;//号码管理
	public static final int MOUDLE_TYPE_PHONE_TQ = 51;//统签号码管理
	public static final int MOUDLE_TYPE_AUDIT_MY = 60;//我的申请
	public static final int MOUDLE_TYPE_AUDIT = 61;//审核管理
	public static final int MOUDLE_TYPE_RECORD = 70;//通话管理
	public static final int MOUDLE_TYPE_MISSCALL = 71;//漏话管理
	public static final int MOUDLE_TYPE_SERVER_LEVEL = 72;//服务评级
	public static final int MOUDLE_TYPE_STAT = 80;//统计分析
	public static final int MOUDLE_TYPE_REPORT = 90;//报告管理
	public static final int MOUDLE_TYPE_DOWNLOAD = 91;//下载管理
	public static final int MOUDLE_TYPE_SYSTEM = 100;//系统管理

	public static final int OPER_TYPE_LOGIN = 1;//登录
	public static final int OPER_TYPE_LOGOUT = 2;//登出
	public static final int OPER_TYPE_CHANGE_PWD = 3;//修改密码
	public static final int OPER_TYPE_ADD = 10;//新增
	public static final int OPER_TYPE_EDIT = 20;//修改
	public static final int OPER_TYPE_DEL = 30;//删除
	public static final int OPER_TYPE_IMPORT = 40;//导入
	public static final int OPER_TYPE_EXPORT = 50;//导出
	public static final int OPER_TYPE_WORKTIME = 51;//工作时间
	public static final int OPER_TYPE_EXTEND_WORKTIME = 52;//延长工作时间
	public static final int OPER_TYPE_WELCOME_WORD = 53;//欢迎语
	public static final int OPER_TYPE_BUSY_TIP = 54;//遇忙提示
	public static final int OPER_TYPE_NO_WORKTIME_TIP = 55;//非工作时间提示
	public static final int OPER_TYPE_SATISFACTION_EVALUATION = 56;//满意度评价开关
	public static final int OPER_TYPE_RECORD_WHITE_LIST = 57;//录音白名单
	public static final int OPER_TYPE_CALL_IN_BLACK_LIST = 58;//呼入黑名单
	public static final int OPER_TYPE_CALL_IN_COLLABORATIVE = 59;//协同号码
	public static final int OPER_TYPE_CALL_NUMBER = 60;//通话并发数
	public static final int OPER_TYPE_MISSCALL_TAG = 70;//漏话标记
	public static final int OPER_TYPE_DOWNLOAD_DAY = 80;//日报下载
	public static final int OPER_TYPE_DOWNLOAD_WEEK = 81;//周报下载
	public static final int OPER_TYPE_DOWNLOAD_MONTH = 82;//月报下载
	public static final int OPER_TYPE_DOWNLOAD_FILE = 90;//文件下载
	public static final int OPER_TYPE_SYNERGY_INTERCEPT_CONFIG = 100;//协同号码拦截配置
	public static final int OPER_TYPE_REPORT_TEMPLATE_CONFIG = 101;//报告模板配置



	
	public static void log(int operationModule, String phoneId, String operationPhone, int operationType, String operationDesc, YCUserPrincipal user, HttpServletRequest request){
		EasyRecord record = new EasyRecord("ZSJ_OPERATION_LOG", "id");
		record.setPrimaryValues(RandomKit.orderId());
		record.set("user_id", user.getUserId());
		record.set("user_name", user.getUserName());
		record.set("user_account", user.getLoginAcct());
		record.set("operation_time", EasyDate.getCurrentDateString() );
		record.set("operation_module", operationModule);
		record.set("phone_id", phoneId);
		record.set("operation_phone", operationPhone);
		record.set("group_id", getDeptInfo(user.getUserId(), SchemaService.findSchemaByEntId(user.getEntId())).getString("DEPT_ID"));
		record.set("group_code", getDeptInfo(user.getUserId(), SchemaService.findSchemaByEntId(user.getEntId())).getString("DEPT_CODE"));
		record.set("group_name", getDeptInfo(user.getUserId(), SchemaService.findSchemaByEntId(user.getEntId())).getString("DEPT_NAME"));
		record.set("operation_type", operationType);
		record.set("operation_desc", operationDesc);
		record.set("operation_ip", IpUtil.getIpAddr(request));
		record.set("create_time", EasyDate.getCurrentDateString());
		try {
			QueryFactory.getWriteQuery().save(record);
		} catch (SQLException e) {
			CommonLogger.getLogger().error(e.getMessage(),e);
		}
	}


	public static void log(int operationModule, String phoneId, String operationPhone, int operationType, String operationDesc, HttpServletRequest request){
		UserModel user = UserUtil.getUser(request);

		EasyRecord record = new EasyRecord("ZSJ_OPERATION_LOG", "id");
		record.setPrimaryValues(RandomKit.orderId());
		record.set("user_id", user.getUserId());
		record.set("user_name", user.getUserName());
		record.set("user_account", user.getUserAcct());
		record.set("operation_time", EasyDate.getCurrentDateString() );
		record.set("operation_module", operationModule);
		record.set("phone_id", phoneId);
		record.set("operation_phone", operationPhone);
		record.set("group_id", getDeptInfo(user.getUserId(), user.getSchemaName()).getString("DEPT_ID"));
		record.set("group_code", getDeptInfo(user.getUserId(), user.getSchemaName()).getString("DEPT_CODE"));
		record.set("group_name", getDeptInfo(user.getUserId(), user.getSchemaName()).getString("DEPT_NAME"));
		record.set("operation_type", operationType);
		record.set("operation_desc", operationDesc);
		record.set("operation_ip", IpUtil.getIpAddr(request));
		record.set("create_time", EasyDate.getCurrentDateString());
		try {
			QueryFactory.getWriteQuery().save(record);
		} catch (SQLException e) {
			CommonLogger.getLogger().error(e.getMessage(),e);
		}
	}

	//获取用户部门信息
	public static JSONObject getDeptInfo(String userId, String schemaName) {
		try {
			//添加用户1分钟缓存
			String key = "CC_USER_DEPT_" + userId;
			String string = CacheUtil.get(key);
			if(StringUtils.isNotBlank(string)) {
				return JSONObject.parseObject(string);
			}
			EasySQL sql = new EasySQL("select t2.SKILL_GROUP_ID DEPT_ID,t2.SKILL_GROUP_CODE DEPT_CODE,t2.SKILL_GROUP_NAME DEPT_NAME from "
					+ schemaName + ".cc_skill_group_user t1");
			sql.append(" inner join " + schemaName + ".cc_skill_group t2 on t1.SKILL_GROUP_ID=t2.SKILL_GROUP_ID where 1=1 ");
			sql.append(userId," and t1.USER_ID=? ",false);
			JSONObject forRow = QueryFactory.getWriteQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			CacheUtil.put(key, forRow.toJSONString(), 60);
			return forRow;
		} catch (Exception e) {
			CommonLogger.getLogger().error(e.getMessage(),e);
		}
		return new JSONObject();
	}
}

//
//create table zsj_operation_log(
//id                   varchar(36) not null comment '主键ID',
//user_id              varchar(36) not null comment '操作人ID（关联CC_USER表）',
//user_name            varchar(50) not null comment '操作人姓名',
//user_account         varchar(50) not null comment '操作人账号',
//operation_time       varchar(19) not null comment '操作时间',
//operation_module     varchar(50) not null comment '操作模块',
//phone_id             varchar(36) comment '号码ID（关联zsj_phone_number表）',
//operation_phone      varchar(20) default NULL comment '操作号码',
//group_id             varchar(36) default NULL comment '操作号码所属单位ID（关联CC_SKILL_GROUP表）',
//group_name           varchar(100) default NULL comment '操作号码所属单位名称',
//operation_type       varchar(50) not null comment '操作类型',
//operation_desc       varchar(500) not null comment '操作描述',
//operation_ip         varchar(50) default NULL comment '操作IP',
//create_time          varchar(19) not null comment '创建时间',
//primary key (id),
//key idx_user_id (user_id),
//key idx_operation_time (operation_time),
//key idx_operation_module (operation_module),
//key idx_operation_phone (operation_phone)
//)
//ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
