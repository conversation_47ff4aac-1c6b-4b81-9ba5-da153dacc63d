package com.yunqu.yc.govphonemgmt.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.govphonemgmt.base.CommonLogger;
import com.yunqu.yc.govphonemgmt.base.Constants;
import com.yunqu.yc.govphonemgmt.util.DateUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  号码操作审批完成处理器
 */
public class PhoneNumHandler extends CompleteApplicationHandler {

    private static Logger logger = CommonLogger.getLogger("phoneNumber");

    static {
        register(Constants.GROUP_AUDIT_TYPE_1, new PhoneNumHandler());
    }

    @Override
    public void handle(EasyRow application, EasyQuery query, UserPrincipal userPrincipal, Boolean isSuccess) throws Exception {
        String applicationId = application.getColumnValue("id");
        String operateType = application.getColumnValue("operate_type");
        // 查询到所有的记录
        EasySQL easySQL = new EasySQL("select * from zsj_application_version where 1 = 1 ");
        easySQL.append(applicationId, " and application_id = ? ");
        List<EasyRow> versionList = query.queryForList(easySQL.getSQL(), easySQL.getParams());
        if (versionList == null || versionList.isEmpty()) return;
        List<String> numberIds = new ArrayList<>();
        for (EasyRow version : versionList) {
            JSONObject json = JSON.parseObject(version.getColumnValue("obj_json"));
            String busiId = json.getString("busiId");
            numberIds.add(busiId);
        }
        String[] numberIdArr = numberIds.toArray(new String[0]);
        switch (operateType) {
            case Constants.OPERATE_TYPE_1:
                // 新增  审批通过之后将number_status都改为1 => 已生效 不通过则改为2 => 已删除
                int status = isSuccess ? 1 : 2;
                EasySQL easySQL1 = new EasySQL("update zsj_phone_number set number_status = "+ status +" where 1 = 1 ");
                easySQL1.appendIn(numberIdArr, "and id ");
                query.execute(easySQL1.getSQL(), easySQL1.getParams());
                // 初始化满意度配置  初始化通话并发数配置
                initSatisfactionConfig(numberIdArr, query);
                initConcurrentCallConfig(numberIdArr, query);
                break;
            case Constants.OPERATE_TYPE_2:
                // 修改 无
                break;
            case Constants.OPERATE_TYPE_3:
                // 删除
                if (isSuccess) {
                    //  审批通过之后将number_status都改为2 => 已删除
                    EasySQL easySQL2 = new EasySQL("update zsj_phone_number set number_status = 2 where 1 = 1 ");
                    easySQL2.appendIn(numberIdArr, "and id ");
                    query.execute(easySQL2.getSQL(), easySQL2.getParams());
                }
                break;
            case Constants.OPERATE_TYPE_4:
                // 导出  找到downloadTaskId  并将对应的下载任务表的 task_status 改为2
                if (isSuccess) {
                    EasyRow version = versionList.get(0);
                    JSONObject json = JSON.parseObject(version.getColumnValue("obj_json"));
                    String downloadTaskId = json.getString("downloadTaskId");
                    EasySQL easySQL2 = new EasySQL("update zsj_download_task set task_status = 2 where 1 = 1 ");
                    easySQL2.append(downloadTaskId, "and id ? ");
                    query.execute(easySQL2.getSQL(), easySQL2.getParams());
                }
                break;
        }
    }


    /**
     *  初始化满意度开关配置，默认为关闭
     *
     * @param numberIdArr
     */
    private void initSatisfactionConfig(String[] numberIdArr, EasyQuery query) throws  Exception {
        if (numberIdArr == null || numberIdArr.length == 0) return;
        String now = DateUtil.formatDate(new Date());
        for (String phoneId : numberIdArr) {
            EasyRecord record = new EasyRecord("zsj_satisfaction_switch_detail", "id");
            record.set("id", RandomKit.randomStr());
            record.set("phone_id", phoneId);
            record.set("switch_status", 0); // 关闭
            record.set("create_time", now);
            record.set("update_time", now);
            // 已生效
            record.set("satisfaction_status", 1);

            query.save( record);
        }
    }

    /**
     *  初始化通话并发数配置，默认为1
     *
     * @param numberIdArr
     */
    private void initConcurrentCallConfig(String[] numberIdArr, EasyQuery query) throws  Exception {
        if (numberIdArr == null || numberIdArr.length == 0) return;
        String now = DateUtil.formatDate(new Date());
        for (String phoneId : numberIdArr) {
            EasyRecord record = new EasyRecord("zsj_concurrent_call_detail", "id");
            record.set("id", RandomKit.randomStr());
            record.set("phone_id", phoneId);
            record.set("concurrent_call_limit", 1);
            record.set("create_time", now);
            record.set("update_time", now);
            // 已生效
            record.set("satisfaction_status", 1);

            query.save( record);
        }
    }
}
