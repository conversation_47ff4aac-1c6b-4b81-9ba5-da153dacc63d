package com.yunqu.yc.govphonemgmt.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.govphonemgmt.base.AppDaoContext;
import com.yunqu.yc.govphonemgmt.base.CommonLogger;
import com.yunqu.yc.govphonemgmt.base.Constants;
import com.yunqu.yc.govphonemgmt.dao.sql.CallStatSql;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import java.util.List;

/**
 * 报表层
 */
@WebObject(name = "callStatDao")
public class CallStatDao extends AppDaoContext {


	private static Logger logger = CommonLogger.getLogger();

	protected EasyQuery getQuery() {
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
		query.setTimeout(600);
		return query;
	}

	/**
	 * 呼入话务报表汇总
	 * @return
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-callin"},  msg="没有权限")
	@WebControl(name = "getCallInStatInfo", type = Types.LIST)
	public JSONObject getCallInStatInfo() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		EasySQL sql = CallStatSql.getCallInStatSql(param);
		JSONObject result = this.queryForList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumCallInStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		result.put("data", list);
		return result;
	}

	/**
	 * 呼入话务报表明细
	 * @return
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-callin"},  msg="没有权限")
	@WebControl(name = "getCallInStatList", type = Types.LIST)
	public JSONObject getCallInStatList() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		EasySQL sql = CallStatSql.getCallInStatSql(param);
		JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumCallInStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		result.put("data", list);
		return result;
	}

	//呼入话务量接通率、人均通时
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-callin"},  msg="没有权限")
	@WebControl(name = "getCallInHourStat", type = Types.LIST)
	public JSONObject getCallInHourStat() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		param.put("dimension", "4");//小时
		EasySQL sql = CallStatSql.getCallInStatSql(param);
		JSONObject result = this.queryForList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumCallInStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		JSONObject formatData = formatData(list);
		result.put("data", formatData);
		return result;
	}


	/**
	 * 呼出话务报表汇总
	 * @return
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-callout"},  msg="没有权限")
	@WebControl(name = "getCallOutStatInfo", type = Types.LIST)
	public JSONObject getCallOutStatInfo() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		EasySQL sql = CallStatSql.getCallOutStatSql(param);
		JSONObject result = this.queryForList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumCallOutStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		result.put("data", list);
		return result;
	}

	/**
	 * 呼出话务报表明细
	 * @return
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-callout"},  msg="没有权限")
	@WebControl(name = "getCallOutStatList", type = Types.LIST)
	public JSONObject getCallOutStatList() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		EasySQL sql = CallStatSql.getCallOutStatSql(param);
		JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumCallOutStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		result.put("data", list);
		return result;
	}

	//呼出话务量接通率、人均通时
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-callout"},  msg="没有权限")
	@WebControl(name = "getCallOutHourStat", type = Types.LIST)
	public JSONObject getCallOutHourStat() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		param.put("dimension", "4");//小时
		EasySQL sql = CallStatSql.getCallOutStatSql(param);
		JSONObject result = this.queryForList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumCallOutStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		JSONObject formatData = formatData(list);
		result.put("data", formatData);
		return result;
	}

	/**
	 * 满意度分析汇总
	 * @return
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-satisfaction"},  msg="没有权限")
	@WebControl(name = "getSatisfactionStatInfo", type = Types.LIST)
	public JSONObject getSatisfactionStatInfo() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		EasySQL sql = CallStatSql.getSatisfactionStatSql(param);
		JSONObject result = this.queryForList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumSatisfactionStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		result.put("data", list);
		return result;
	}

	/**
	 * 满意度分析汇总明细
	 * @return
	 */
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-satisfaction"},  msg="没有权限")
	@WebControl(name = "getSatisfactionStatList", type = Types.LIST)
	public JSONObject getSatisfactionStatList() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		EasySQL sql = CallStatSql.getSatisfactionStatSql(param);
		JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumSatisfactionStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		result.put("data", list);
		return result;
	}

	//满意度分析满意率浮动分析，呼入数与参评率分析
	@PreAuthorize(resId = {"yc-govphonemgmt-stat-satisfaction"},  msg="没有权限")
	@WebControl(name = "getSatisfactionHourStat", type = Types.LIST)
	public JSONObject getSatisfactionHourStat() {
		if(isNullVerify(param)){
			return EasyResult.fail("开始结束时间不能为空");
		}
		this.setQuery(getQuery());
		param.put("dimension", "4");//小时
		EasySQL sql = CallStatSql.getSatisfactionStatSql(param);
		JSONObject result = this.queryForList(sql.getSQL(), sql.getParams());
		List<JSONObject> list = CallStatSql.sumSatisfactionStat(result.getJSONArray("data").toJavaList(JSONObject.class),false);
		JSONObject formatData = formatData(list);
		result.put("data", formatData);
		return result;
	}


	/**
	 * 构造适合前端的数据结构
	 */
	private JSONObject formatData(List<JSONObject> list) {
		JSONObject result = new JSONObject();
		//1小时维度
		String[] axis = {"00:00","01:00","02:00","03:00","04:00","05:00","06:00","07:00","08:00","09:00","10:00","11:00","12:00",
				"13:00","14:00","15:00","16:00","17:00","18:00","19:00","20:00","21:00","22:00","23:00"};

		//呼入报表
		String[] callInPercent  = new String[axis.length];
		String[] callInTimeAvg  = new String[axis.length];
		//呼出报表
		String[] callOutPercent  = new String[axis.length];
		String[] callOutTimeAvg  = new String[axis.length];
		//满意度报表
		String[] evaluateGoodPercent  = new String[axis.length];
		String[] evaluatePercent  = new String[axis.length];
		String[] callInCount = new String[axis.length];
		for(Object object : list) {
			JSONObject json= (JSONObject) object;
			for(int i=0;i<axis.length;i++) {
				if(axis[i].equals(json.getString("SELECT_TYPE")+":00")) {
					callInPercent[i]  = json.getString("CALL_IN_PERCENT");
					callInTimeAvg[i]  = json.getString("CALL_IN_TIME_AVG");
					callOutPercent[i]  = json.getString("CALL_OUT_PERCENT");
					callOutTimeAvg[i]  = json.getString("CALL_OUT_TIME_AVG");
					evaluateGoodPercent[i]  = json.getString("SATISF_GOOD_PERCENT");
					evaluatePercent[i]  = json.getString("EVALUATE_PERCENT");
					callInCount[i]  = json.getString("CALL_IN_COUNT");
					break;
				}
			}
		}
		result.put("time", axis);
		result.put("callInPercent"  , callInPercent);
		result.put("callInTimeAvg"  , callInTimeAvg);
		result.put("callOutPercent"  , callOutPercent);
		result.put("callOutTimeAvg"  , callOutTimeAvg);
		result.put("evaluateGoodPercent"  , evaluateGoodPercent);
		result.put("evaluatePercent"  , evaluatePercent);
		result.put("callInCount"  , callInCount);
		return result;
	}

	/**
	 * 非空验证
	 * @param param
	 * @return
	 */
	public boolean isNullVerify(JSONObject param) {
		String beginTime = param.getString("BEGIN_TIME");
		String endTime = param.getString("END_TIME");
		if(StringUtils.isBlank(beginTime)||StringUtils.isBlank(endTime)){
			return true;
		}
		return false;
	}
}
