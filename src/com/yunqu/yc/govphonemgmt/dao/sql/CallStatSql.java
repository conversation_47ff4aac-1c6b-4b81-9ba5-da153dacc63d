package com.yunqu.yc.govphonemgmt.dao.sql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.govphonemgmt.base.CommonLogger;
import com.yunqu.yc.govphonemgmt.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;
import java.util.List;

/**
 * sql
 */
public class CallStatSql {


	protected static EasyQuery getQuery() {
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
		query.setTimeout(600);
		return query;
	}
	private static Logger logger = CommonLogger.getLogger("sql");

	//呼入话务报表
	public static EasySQL getCallInStatSql(JSONObject param) {
		String dimension = param.getString("dimension");
		String type = getCallDimension(dimension);
		EasySQL sql = new EasySQL("");
		sql.append("select  "+type+" SELECT_TYPE,");
		sql.append("  sum(CALL_IN_COUNT)  CALL_IN_COUNT, ");
		sql.append("  sum(CALL_IN_VALID_COUNT) CALL_IN_VALID_COUNT, ");
		sql.append("  sum(CALL_IN_SUCC_COUNT) CALL_IN_SUCC_COUNT, ");
		sql.append("  sum(AGENT_COUNT) AGENT_COUNT, ");
		sql.append("  sum(CALL_IN_TIME) CALL_IN_TIME, ");
		sql.append("  sum(CALL_IN_ALERTING_TIME) CALL_IN_ALERTING_TIME, ");
		sql.append("  sum(CALL_IN_ALERTING_COUNT) CALL_IN_ALERTING_COUNT ");
		sql.append(" from zsj_call_stat_rpt where 1=1 ");
		String beginTime = param.getString("BEGIN_TIME");
		String endTime = param.getString("END_TIME");
		if (StringUtils.isNotBlank(beginTime)&&beginTime.length()<19){
			beginTime = beginTime+" 00:00:00";
		}else{
			beginTime = beginTime.substring(0,19);
		}
		sql.append(beginTime," and  STAT_TIME>=?");
		if (StringUtils.isNotBlank(endTime)&&endTime.length()<19){
			endTime = endTime+" 23:59:59";
		}else{
			endTime = endTime.substring(0,19);
		}
		sql.append(endTime," and STAT_TIME<=?");
		sql.append(" GROUP BY "+type);
		sql.append(" ORDER BY "+type+" asc ");
		logger.info("sql="+sql.getSQL()+"{"+ JSON.toJSONString(sql.getParams())+"}");
		return sql;
	}

	//呼入话务报表
	public static EasySQL getCallOutStatSql(JSONObject param) {
		String dimension = param.getString("dimension");
		String type = getCallDimension(dimension);
		EasySQL sql = new EasySQL("");
		sql.append("select  "+type+" SELECT_TYPE,");
		sql.append("  sum(CALL_OUT_COUNT)  CALL_OUT_COUNT, ");
		sql.append("  sum(CALL_OUT_TIME) CALL_OUT_TIME, ");
		sql.append("  sum(CALL_OUT_SUCC_COUNT) CALL_OUT_SUCC_COUNT, ");
		sql.append("  sum(CALL_OUT_ALERTING_TIME) CALL_OUT_ALERTING_TIME, ");
		sql.append("  sum(CALL_OUT_ALERTING_COUNT) CALL_OUT_ALERTING_COUNT ");
		sql.append(" from zsj_call_stat_rpt where 1=1 ");
		String beginTime = param.getString("BEGIN_TIME");
		String endTime = param.getString("END_TIME");
		if (StringUtils.isNotBlank(beginTime)&&beginTime.length()<19){
			beginTime = beginTime+" 00:00:00";
		}else{
			beginTime = beginTime.substring(0,19);
		}
		sql.append(beginTime," and  STAT_TIME>=?");
		if (StringUtils.isNotBlank(endTime)&&endTime.length()<19){
			endTime = endTime+" 23:59:59";
		}else{
			endTime = endTime.substring(0,19);
		}
		sql.append(endTime," and STAT_TIME<=?");
		sql.append(" GROUP BY "+type);
		sql.append(" ORDER BY "+type+" asc ");
		logger.info("sql="+sql.getSQL()+"{"+ JSON.toJSONString(sql.getParams())+"}");
		return sql;
	}

	//满意度报表
	public static EasySQL getSatisfactionStatSql(JSONObject param) {
		String dimension = param.getString("dimension");
		String type = getCallDimension(dimension);
		EasySQL sql = new EasySQL("");
		sql.append("select  "+type+" SELECT_TYPE,");
		sql.append("  sum(CALL_IN_COUNT)  CALL_IN_COUNT, ");
		sql.append("  sum(EVALUATE_COUNT) EVALUATE_COUNT, ");
		sql.append("  sum(NO_EVALUATE_COUNT) NO_EVALUATE_COUNT, ");
		sql.append("  sum(CALL_IN_SUCC_COUNT) CALL_IN_SUCC_COUNT, ");
		sql.append("  sum(VERY_SATISF_COUNT) VERY_SATISF_COUNT, ");
		sql.append("  sum(SATISF_COUNT) SATISF_COUNT ");
		sql.append(" from zsj_call_stat_rpt where 1=1 ");
		String beginTime = param.getString("BEGIN_TIME");
		String endTime = param.getString("END_TIME");
		if (StringUtils.isNotBlank(beginTime)&&beginTime.length()<19){
			beginTime = beginTime+" 00:00:00";
		}else{
			beginTime = beginTime.substring(0,19);
		}
		sql.append(beginTime," and  STAT_TIME>=?");
		if (StringUtils.isNotBlank(endTime)&&endTime.length()<19){
			endTime = endTime+" 23:59:59";
		}else{
			endTime = endTime.substring(0,19);
		}
		sql.append(endTime," and STAT_TIME<=?");
		sql.append(" GROUP BY "+type);
		sql.append(" ORDER BY "+type+" asc ");
		logger.info("sql="+sql.getSQL()+"{"+ JSON.toJSONString(sql.getParams())+"}");
		return sql;
	}



	//呼入报表汇总处理
	public  static List<JSONObject> sumCallInStat(List<JSONObject> array,boolean isSum){
		if(array==null||array.size()==0){
			return array;
		}
		int CALL_IN_COUNT=0;
		int CALL_IN_VALID_COUNT=0;
		int CALL_IN_SUCC_COUNT=0;
		int AGENT_COUNT=0;
		int CALL_IN_TIME=0;
		int CALL_IN_ALERTING_TIME=0;
		int CALL_IN_ALERTING_COUNT=0;
		for (JSONObject record : array) {
			CALL_IN_COUNT += record.getIntValue("CALL_IN_COUNT");
			CALL_IN_VALID_COUNT += record.getIntValue("CALL_IN_VALID_COUNT");
			CALL_IN_SUCC_COUNT += record.getIntValue("CALL_IN_SUCC_COUNT");
			AGENT_COUNT += record.getIntValue("AGENT_COUNT");
			CALL_IN_TIME += record.getIntValue("CALL_IN_TIME");
			CALL_IN_ALERTING_TIME += record.getIntValue("CALL_IN_ALERTING_TIME");
			CALL_IN_ALERTING_COUNT += record.getIntValue("CALL_IN_ALERTING_COUNT");
			record.put("CALL_IN_PERCENT",getPercent(record.getIntValue("CALL_IN_SUCC_COUNT"),record.getIntValue("CALL_IN_COUNT")));
			record.put("CALL_LOSS_COUNT",record.getIntValue("AGENT_COUNT")-record.getIntValue("CALL_IN_SUCC_COUNT"));
			record.put("CALL_LOSS_PERCENT",getPercent(record.getIntValue("AGENT_COUNT")-record.getIntValue("CALL_IN_SUCC_COUNT"),record.getIntValue("AGENT_COUNT")));
			record.put("CALL_IN_TIME_AVG",getDoubleDecimal(record.getIntValue("CALL_IN_TIME"),record.getIntValue("CALL_IN_SUCC_COUNT"),0));
			record.put("CALL_IN_ALERTING_AVG",getDoubleDecimal(record.getIntValue("CALL_IN_ALERTING_TIME"),record.getIntValue("CALL_IN_ALERTING_COUNT"),0));
		}
		if (isSum){
			JSONObject sum= new JSONObject();
			sum.put("SELECT_TYPE", "合计");
			sum.put("CALL_IN_COUNT", CALL_IN_COUNT);
			sum.put("CALL_IN_VALID_COUNT", CALL_IN_VALID_COUNT);
			sum.put("CALL_IN_SUCC_COUNT", CALL_IN_SUCC_COUNT);
			sum.put("AGENT_COUNT", AGENT_COUNT);
			sum.put("CALL_IN_TIME", CALL_IN_TIME);
			sum.put("CALL_IN_ALERTING_TIME", CALL_IN_ALERTING_TIME);
			sum.put("CALL_IN_PERCENT",getPercent(CALL_IN_SUCC_COUNT,CALL_IN_COUNT));
			sum.put("CALL_LOSS_COUNT",AGENT_COUNT-CALL_IN_SUCC_COUNT);
			sum.put("CALL_LOSS_PERCENT",getPercent(AGENT_COUNT-CALL_IN_SUCC_COUNT,AGENT_COUNT));
			sum.put("CALL_IN_TIME_AVG",getDoubleDecimal(CALL_IN_TIME,CALL_IN_SUCC_COUNT,0));
			sum.put("CALL_IN_ALERTING_AVG",getDoubleDecimal(CALL_IN_ALERTING_TIME,CALL_IN_ALERTING_COUNT,0));
			array.add(sum);
		}
		return  array;
	}

	//呼出报表汇总处理
	public  static List<JSONObject> sumCallOutStat(List<JSONObject> array,boolean isSum){
		if(array==null||array.size()==0){
			return array;
		}
		int CALL_OUT_COUNT=0;
		int CALL_OUT_TIME=0;
		int CALL_OUT_SUCC_COUNT=0;
		int CALL_OUT_ALERTING_TIME=0;
		int CALL_OUT_ALERTING_COUNT=0;

		for (JSONObject record : array) {
			CALL_OUT_COUNT += record.getIntValue("CALL_OUT_COUNT");
			CALL_OUT_TIME += record.getIntValue("CALL_OUT_TIME");
			CALL_OUT_SUCC_COUNT += record.getIntValue("CALL_OUT_SUCC_COUNT");
			CALL_OUT_ALERTING_TIME += record.getIntValue("CALL_OUT_ALERTING_TIME");
			CALL_OUT_ALERTING_COUNT += record.getIntValue("CALL_OUT_ALERTING_COUNT");
			record.put("CALL_OUT_FAIL_COUNT",record.getIntValue("CALL_OUT_COUNT")-record.getIntValue("CALL_OUT_SUCC_COUNT"));
			record.put("CALL_OUT_PERCENT",getPercent(record.getIntValue("CALL_OUT_SUCC_COUNT"),record.getIntValue("CALL_OUT_COUNT")));
			record.put("CALL_OUT_TIME_AVG",getDoubleDecimal(record.getIntValue("CALL_OUT_TIME"),record.getIntValue("CALL_OUT_SUCC_COUNT"),0));
			record.put("CALL_OUT_ALERTING_AVG",getDoubleDecimal(record.getIntValue("CALL_OUT_ALERTING_TIME"),record.getIntValue("CALL_OUT_ALERTING_COUNT"),0));
		}
		if (isSum){
			JSONObject sum= new JSONObject();
			sum.put("SELECT_TYPE", "合计");
			sum.put("CALL_OUT_COUNT", CALL_OUT_COUNT);
			sum.put("CALL_OUT_TIME", CALL_OUT_TIME);
			sum.put("CALL_OUT_SUCC_COUNT", CALL_OUT_SUCC_COUNT);
			sum.put("CALL_OUT_FAIL_COUNT", CALL_OUT_COUNT-CALL_OUT_SUCC_COUNT);
			sum.put("CALL_OUT_ALERTING_TIME", CALL_OUT_ALERTING_TIME);
			sum.put("CALL_OUT_PERCENT",getPercent(CALL_OUT_SUCC_COUNT,CALL_OUT_COUNT));
			sum.put("CALL_OUT_TIME_AVG",getDoubleDecimal(CALL_OUT_TIME,CALL_OUT_SUCC_COUNT,0));
			sum.put("CALL_OUT_ALERTING_AVG",getDoubleDecimal(CALL_OUT_ALERTING_TIME,CALL_OUT_ALERTING_COUNT,0));
			array.add(sum);
		}
		return  array;
	}

	//呼出报表汇总处理
	public  static List<JSONObject> sumSatisfactionStat(List<JSONObject> array,boolean isSum){
		if(array==null||array.size()==0){
			return array;
		}
		int CALL_IN_COUNT=0;
		int EVALUATE_COUNT=0;
		int NO_EVALUATE_COUNT=0;
		int CALL_IN_SUCC_COUNT=0;
		int VERY_SATISF_COUNT=0;
		int SATISF_COUNT=0;

		for (JSONObject record : array) {
			CALL_IN_COUNT += record.getIntValue("CALL_IN_COUNT");
			EVALUATE_COUNT += record.getIntValue("EVALUATE_COUNT");
			NO_EVALUATE_COUNT += record.getIntValue("NO_EVALUATE_COUNT");
			CALL_IN_SUCC_COUNT += record.getIntValue("CALL_IN_SUCC_COUNT");
			VERY_SATISF_COUNT += record.getIntValue("VERY_SATISF_COUNT");
			SATISF_COUNT += record.getIntValue("SATISF_COUNT");

			record.put("EVALUATE_PERCENT",getPercent(record.getIntValue("EVALUATE_COUNT"),record.getIntValue("CALL_IN_SUCC_COUNT")));
			record.put("SATISF_GOOD_PERCENT",getPercent(record.getIntValue("VERY_SATISF_COUNT")+record.getIntValue("SATISF_COUNT"),record.getIntValue("EVALUATE_COUNT")));
			record.put("SATISF_GOOD_COUNT",record.getIntValue("VERY_SATISF_COUNT")+record.getIntValue("SATISF_COUNT"));
			record.put("SATISF_BAD_COUNT",record.getIntValue("EVALUATE_COUNT")-record.getIntValue("VERY_SATISF_COUNT")+record.getIntValue("SATISF_COUNT"));
		}
		if (isSum){
			JSONObject sum= new JSONObject();
			sum.put("SELECT_TYPE", "合计");
			sum.put("CALL_IN_COUNT", CALL_IN_COUNT);
			sum.put("EVALUATE_COUNT", EVALUATE_COUNT);
			sum.put("NO_EVALUATE_COUNT", NO_EVALUATE_COUNT);
			sum.put("CALL_IN_SUCC_COUNT", CALL_IN_SUCC_COUNT);
			sum.put("VERY_SATISF_COUNT", VERY_SATISF_COUNT);
			sum.put("SATISF_COUNT", SATISF_COUNT);
			sum.put("EVALUATE_PERCENT",getPercent(EVALUATE_COUNT,CALL_IN_SUCC_COUNT));
			sum.put("SATISF_GOOD_PERCENT",getPercent(VERY_SATISF_COUNT+SATISF_COUNT,EVALUATE_COUNT));
			sum.put("SATISF_GOOD_COUNT",VERY_SATISF_COUNT+SATISF_COUNT);
			sum.put("SATISF_BAD_COUNT",EVALUATE_COUNT-VERY_SATISF_COUNT-SATISF_COUNT);
			array.add(sum);
		}
		return  array;
	}

	private static String getPercent(int num1, int num2) {
		if (0 != num2) {
			double quotient = (double) num1 / (double) num2;
			double percentage = quotient * 100;
			String result = String.format("%.2f", percentage);
			return result + "%";
		} else {
			return "0.00%";
		}

	}

	/**
	 * 获取维度
	 * @param stepId
	 * @return
	 */
	private static String getCallDimension(String dimension) {
		String step = "1";
		if("1".equals(dimension)) {
			step = "DAY ";
		}else if("2".equals(dimension)) {
			step = "PHONE_NUMBER ";
		}else if("3".equals(dimension)) {
			step = "GROUP_ID";
		}else if("4".equals(dimension)) {
			step = "HOUR";
		}
		return step;
	}

	public static String getDoubleDecimal(int num1, int num2, int formatNum) {
		if (0 != num2) {
			double quotient = (double) num1 / (double) num2;
			return String.format("%." + formatNum + "f", quotient);
		} else {
			return "0";
		}
	}



}
