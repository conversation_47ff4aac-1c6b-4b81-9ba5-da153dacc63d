package com.yunqu.yc.govphonemgmt.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.govphonemgmt.base.AppDaoContext;
import com.yunqu.yc.govphonemgmt.base.CommonLogger;
import com.yunqu.yc.govphonemgmt.base.mapper.HumpMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  号码查询
 */

@WebObject(name = "phoneNumber")
public class PhoneNumberDao extends AppDaoContext {

    private static Logger logger = CommonLogger.getLogger("phoneNumber");

    /**
     *  号码列表查询
     *
     * @params:
     *  groupId 单位ID
     *  numberSource 号码来源
     *  numberType 号码类型
     *  number 号码
     *
     * @return
     */
    @WebControl(name = "list", type = Types.LIST)
    public JSONObject list() {
        List<JSONObject> phoneNumList = getPhoneNumList(param, this.getQuery());
        return EasyResult.ok(phoneNumList);
    }

   public static List<JSONObject> getPhoneNumList(JSONObject params, EasyQuery query) {
       List<JSONObject> result = new ArrayList<>();

       // 单位ID
       String groupId = params.getString("groupId");
       // 号码来源
       Integer numberSource = params.getInteger("numberSource");
       // 号码类型
       Integer numberType = params.getInteger("numberType");
       // 号码
       String number = params.getString("number");
       // 是否设置工作时间
       Integer hasWorkTime = params.getInteger("hasWorkTime");
       // 是否设置欢迎语
       Integer hasWelcomeWord = params.getInteger("hasWelcomeWord");
       // 是否设置遇忙提示
       Integer hasBusyTip = params.getInteger("hasBusyTip");
       // 是否设置非工作时间提示语
       Integer hasNoWorkTimeTip = params.getInteger("hasNoWorkTimeTip");
       // 是否开启满意度评价开关
       Integer hasSatisfactionEvaluation = params.getInteger("hasSatisfactionEvaluation");
       // 是否配置协同号码
       Integer hasCollaboratePhone = params.getInteger("hasCollaboratePhone");
       // 并发数
       Integer concurrentCallLimit = params.getInteger("concurrentCallLimit");

       // 查询当前单位下的子单位ID
       List<String> subGroupIds = new ArrayList<>();
       if (StringUtils.isNotBlank(groupId)) {
           try {
               // 调用递归方法查询所有子部门
               collectSubGroupIdsRecursively(groupId, query, subGroupIds);
           } catch (SQLException e) {
               logger.info("查询部门失败...", e);
               throw new RuntimeException(e);
           }
       }
       // 部门列表
       List<String> groupList = new ArrayList<>();
       groupList.add(groupId);
       groupList.addAll(subGroupIds);

       // 查询号码列表
       EasySQL getPhoneNumSql = new EasySQL("select * from zsj_phone_number where 1=1");
       getPhoneNumSql.appendIn(groupList.toArray(new String[0]), "and group_id ");
       getPhoneNumSql.append(numberSource, "and number_source = ? ");
       getPhoneNumSql.append(numberType, "and number_type = ? ");
       getPhoneNumSql.appendLike(number, "and phone_number like ? ");

       getPhoneNumSql.append(hasWorkTime, "and has_work_time = ? ");
       getPhoneNumSql.append(hasWelcomeWord, "and has_welcome_audio = ? ");
       getPhoneNumSql.append(hasBusyTip, "and has_busy_prompt = ? ");
       getPhoneNumSql.append(hasNoWorkTimeTip, "and has_non_work_time_prompt = ? ");
       getPhoneNumSql.append(hasSatisfactionEvaluation, "and satisfaction_switch = ? ");
       getPhoneNumSql.append(hasCollaboratePhone, "and has_collaborative_number = ? ");
       getPhoneNumSql.append(concurrentCallLimit, "and concurrent_call_limit = ? ");

       getPhoneNumSql.append(1 ,"and number_status = ? ");
       getPhoneNumSql.append("order by create_time desc");

       try {
           result = query.queryForList(getPhoneNumSql.getSQL(), getPhoneNumSql.getParams(), new HumpMapper());
           if (result != null && !result.isEmpty()) {
               // 查询附加信息
               result = result.stream().map(phoneNum -> {
                   String phoneId = phoneNum.getString("id");

                   try {
                       // 查询工作时间
                       EasySQL getWorkTimeSql = new EasySQL("select work_time1_start,work_time1_end,work_time2_start,work_time2_end,work_time3_start,work_time3_end,work_days,skip_holiday ");
                       getWorkTimeSql.append("from zsj_work_time where 1 = 1 ");
                       getWorkTimeSql.append(phoneId, " and phone_id = ? ");
                       JSONObject workTime = query.queryForRow(getWorkTimeSql.getSQL(), getWorkTimeSql.getParams(), new HumpMapper());
                       if (workTime != null) {
                           phoneNum.putAll(workTime);
                       }

                       // 查询协同号码
                       EasySQL getCollaboratePhoneSql = new EasySQL("select collaborative_number1,collaborative_number2 ");
                       getCollaboratePhoneSql.append("from zsj_collaborative_number where 1 = 1 ");
                       getCollaboratePhoneSql.append(phoneId, "and phone_id = ? ");
                       JSONObject collaboratePhone = query.queryForRow(getCollaboratePhoneSql.getSQL(), getCollaboratePhoneSql.getParams(), new HumpMapper());
                       if (collaboratePhone != null) {
                           phoneNum.putAll(collaboratePhone);
                       }

                       // 查询通话并发数
                       EasySQL getCallConcurrentSql = new EasySQL("select concurrent_call_limit");
                       getCallConcurrentSql.append("from zsj_concurrent_call_detail where 1 = 1 ");
                       getCallConcurrentSql.append(phoneId, "and phone_id = ? ");
                       String callConcurrentStr = query.queryForString(getCallConcurrentSql.getSQL(), getCallConcurrentSql.getParams());
                       if (StringUtils.isNotBlank(callConcurrentStr)) {
                           phoneNum.put("concurrentCallLimit", callConcurrentStr);
                       }

                       // 查询欢迎语、遇忙提示语、非工作时间提示语
                       EasySQL getAudioFileSql = new EasySQL("select * from zsj_audio_file where 1 = 1 ");
                       getAudioFileSql.append(phoneId, "and phone_id = ? ");
                       getAudioFileSql.append(1 , " and satisfaction_status = ? ");  // 已生效
                       List<JSONObject> audioFileList = query.queryForList(getAudioFileSql.getSQL(), getAudioFileSql.getParams(), new HumpMapper());
                       if (audioFileList != null && !audioFileList.isEmpty()) {
                           for (JSONObject audioFile : audioFileList) {
                               switch (audioFile.getString("audioType")) {
                                   case "1":
                                       // 欢迎语
                                       phoneNum.put("welcomeAudioFileId", audioFile.getString("id"));
                                       phoneNum.put("welcomeAudioFileName", audioFile.getString("fileName"));
                                       break;
                                   case "2":
                                       // 遇忙提示
                                       phoneNum.put("welcomeAudioFileId", audioFile.getString("id"));
                                       phoneNum.put("welcomeAudioFileName", audioFile.getString("fileName"));
                                   case "3":
                                       // 非工作时间提示语
                                       phoneNum.put("welcomeAudioFileId", audioFile.getString("id"));
                                       phoneNum.put("welcomeAudioFileName", audioFile.getString("fileName"));
                               }
                           }
                       }
                   } catch (SQLException e) {
                       logger.info("查询号码失败...", e);
                       throw new RuntimeException(e);
                   }
                   return phoneNum;
               }).collect(Collectors.toList());

               return result;
           }
       } catch (Exception e) {
           logger.info("查询号码失败...", e);
           throw new RuntimeException(e);
       }

       return result;
    }

    /**
     * 递归查询所有子部门ID
     *
     * @param parentId 父部门ID
     * @param query EasyQuery 实例
     * @param subGroupIds 存储所有子部门ID的列表
     */
    private static void collectSubGroupIdsRecursively(String parentId, EasyQuery query, List<String> subGroupIds) throws SQLException {
        EasySQL findSubGroupIdSql = new EasySQL("select SKILL_GROUP_ID from CC_SKILL_GROUP where 1 = 1 ");
        findSubGroupIdSql.append(parentId, "and P_GROUP_ID = ? ");

        List<JSONObject> subGroupList = query.queryForList(findSubGroupIdSql.getSQL(), findSubGroupIdSql.getParams(), new HumpMapper());

        if (subGroupList != null && !subGroupList.isEmpty()) {
            for (JSONObject item : subGroupList) {
                String skillGroupId = item.getString("skillGroupId");
                if (StringUtils.isBlank(skillGroupId)) continue;
                subGroupIds.add(skillGroupId); // 添加当前子部门
                collectSubGroupIdsRecursively(skillGroupId, query, subGroupIds); // 递归查询下一层级
            }
        }
    }

}
