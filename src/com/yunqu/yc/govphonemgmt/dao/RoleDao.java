package com.yunqu.yc.govphonemgmt.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.govphonemgmt.base.AppDaoContext;
import com.yunqu.yc.govphonemgmt.base.Constants;
import com.yunqu.yc.govphonemgmt.base.mapper.HumpMapper;
import com.yunqu.yc.govphonemgmt.util.TreeUtils;
import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.util.List;

@WebObject(name = "role")
public class RoleDao extends AppDaoContext {

//    @PreAuthorize(resId = "yc-govphonemgmt-org-role")
    @WebControl(name = "roleList", type = Types.LIST)
    public JSONObject roleList() {
        EasySQL sql = new EasySQL("select t1.ROLE_ID,t1.ROLE_NAME,t1.ROLE_TYPE,t1.SYS_FLAG,t1.ROLE_DESC");
        sql.append("from " + this.getTableName("CC_ROLE") + " t1");
        sql.append("where 1=1");
        sql.append(this.getEntId(), "and t1.ENT_ID = ?");
        sql.append(this.getBusiOrderId(), "and t1.BUSI_ORDER_ID = ?");
        sql.append("order by t1.ROLE_TYPE,t1.CREATE_TIME");
        return this.queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
    }

//    @PreAuthorize(resId = "yc-govphonemgmt-org-role")
    @WebControl(name = "roleResTree", type = Types.TREE)
    public JSONObject roleResTree() {
        try {
            String roleId = param.getString("roleId");
            EasySQL sql = new EasySQL("select t1.RES_ID,t1.RES_NAME,t1.P_RES_ID,t2.ROLE_ID");
            sql.append("from CC_BUSI_RES t1");
            sql.append("left join CC_ENT_BUSI_RES t3 on t1.BUSI_ID = t3.BUSI_ID and t1.RES_ID = t3.RES_ID");
            sql.append("left join " + this.getTableName("CC_ROLE_RES") + " t2 on t3.RES_ID = t2.RES_ID and t3.ENT_ID = t2.ENT_ID");
            sql.append(roleId, "and t2.ROLE_ID = ?", false);
            sql.append("where 1=1");
            sql.append(this.getBusiId(), "and t1.BUSI_ID = ?");
            sql.append(this.getEntId(), "and t3.ENT_ID = ?");
            sql.append("and exists (select 1 from CC_BUSI_RES t4 where t4.BUSI_ID = t3.BUSI_ID and t4.RES_ID=t3.RES_ID and t4.RES_STATE !=1  )");
            sql.append("order by T1.IDX_ORDER ASC");
            List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new HumpMapper());
            for(JSONObject row : list) {
                row.put("checked", StringUtils.isBlank(row.getString("roleId")) ? false : true);
            }

            list = TreeUtils.mergeTree(list, "resId", "pResId", "children", "2000");
            return EasyResult.ok(list);
        } catch (Exception e) {
            logger.error("error:" + e.getMessage(), e);
        }
        return EasyResult.fail();
    }

    /**
     * 角色用户列表
     * @return
     */
    @WebControl(name = "roleUserList", type = Types.LIST)
    public JSONObject roleUserList() {
        EasySQL sql = new EasySQL("select t2.USER_ID,T2.USERNAME USER_NAME,t1.CREATE_TIME,t1.CREATOR,");
        sql.append("t2.MOBILE,t4.SKILL_GROUP_NAME DEPT_NAME");
        sql.append("from " + this.getTableName("CC_ROLE_USER") + " t1");
        sql.append("left join " + this.getTableName("CC_USER") + " t2 on t1.USER_ID = t2.USER_ID");
        sql.append("left join " + this.getTableName("CC_SKILL_GROUP_USER") + " t3 on t1.USER_ID = t3.USER_ID");
        sql.append("left join " + this.getTableName("CC_SKILL_GROUP") + " t4 on t3.SKILL_GROUP_ID = t4.SKILL_GROUP_ID").append(Constants.GROUP_TYPE_DEPT, "and t4.SKILL_GROUP_TYPE = ?");
        sql.append("where 1=1");
        sql.append(this.getEntId(), "and t1.ENT_ID = ?");
        sql.append(this.getBusiOrderId(), "and t1.BUSI_ORDER_ID = ?");
        sql.append("order by t1.CREATE_TIME");
        return this.queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
    }

    @WebControl(name = "roleSelectedUser", type = Types.LIST)
    public JSONObject roleSelectedUser() {

    }

}
