package com.yunqu.yc.govphonemgmt.dao;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.yc.govphonemgmt.base.AppDaoContext;
import com.yunqu.yc.govphonemgmt.base.CommonLogger;
import com.yunqu.yc.govphonemgmt.base.Constants;
import com.yunqu.yc.govphonemgmt.util.CommHandleUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import java.sql.SQLException;
import java.util.*;

@WebObject(name = "ApplyDao")
public class ApplyDao extends AppDaoContext {

    private Logger logger = CommonLogger.logger;

    /**
     * 审核中列表
     */
    @WebControl(name = "queryApplyIngList", type = Types.LIST)
    public JSONObject queryApplyIngList() {
        return queryApplyListByStatus(1);
    }

    /**
     * 审核通过列表
     */
    @WebControl(name = "queryApplyApprovedList", type = Types.LIST)
    public JSONObject queryApplyApprovedList() {
        return queryApplyListByStatus(2);
    }

    /**
     * 审核不通过列表
     */
    @WebControl(name = "queryApplyRejectedList", type = Types.LIST)
    public JSONObject queryApplyRejectedList() {
        return queryApplyListByStatus(3);
    }

    private JSONObject queryApplyListByStatus(int flowStatus) {
        UserModel userModel = UserUtil.getUser(this.request);
        EasySQL sql = new EasySQL();
        sql.append("SELECT");
        sql.append("    id,");
        sql.append("    application_no,");
        sql.append("    business_type,");
        sql.append("    applicant_id,");
        sql.append("    applicant_name,");
        sql.append("    application_desc,");
        sql.append("    flow_status,");
        sql.append("    close_status,");
        sql.append("    application_time,");
        sql.append("    update_time");
        sql.append("FROM");
        sql.append(this.getTableName("zsj_application"));
        sql.append("WHERE");
        sql.append(flowStatus,"    flow_status = ?");

        String beginDate = param.getString("begin_date");
        if (StringUtils.isNotBlank(beginDate)) {
            sql.append(beginDate+" 00:00:00","AND application_time >= ?");
        }
        String endDate = param.getString("end_date");
        if (StringUtils.isNotBlank(endDate)) {
            sql.append(endDate+" 23:59:59","AND application_time <= ? ");
        }
        String businessType = param.getString("business_type");
        if (StringUtils.isNotBlank(businessType)) {
            sql.append(businessType,"AND business_type = ?");
        }
        String closeStatus = param.getString("close_status");
        if (StringUtils.isNotBlank(closeStatus)) {
            sql.append(closeStatus, "AND close_status = ?");
        }
        sql.append(userModel.getUserId()," and applicant_id=?",false);
        sql.append("ORDER BY application_time DESC");
        logger.info("sql:" + sql.getFullSql());
        return this.queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 号码操作申请-单个号码
     * @return
     */
    @WebControl(name = "queryApplyDetail1", type = Types.RECORD)
    public JSONObject queryApplyDetail1() {
        String applicationId = param.getString("applicationId");
        if (StringUtils.isBlank(applicationId)){
            return EasyResult.error(500,"申请id不能为空");
        }
        /**
        EasySQL sql1 = getApplyDetailSql(applicationNo);
        EasySQL sql2 = getPhoneSql(applicationNo);
        EasySQL sql3 = getApprovalStepSql(applicationNo);
         EasyRow easyRow = query.queryForRow(sql1.getSQL(), sql1.getParams());
         result.put("application_detail", easyRow.toJSONObject());
         EasyRow phoneRow = query.queryForRow(sql2.getSQL(), sql2.getParams());
         result.put("phone_row", phoneRow.toJSONObject());
         EasyRow auditFlow = query.queryForRow(sql3.getSQL(), sql3.getParams());
         result.put("approve_step", auditFlow.toJSONObject());
         *
         * */

        JSONObject jsonObject = new JSONObject();
        EasyQuery query = getQuery();
        try {
            UserModel userModel = UserUtil.getUser(this.request);
            String schema=userModel.getSchemaName();
            //基础信息   //涉及号码  //操作信息
            JSONObject obj= CommHandleUtil.getInstance().getApplicationInfo(applicationId,schema,query,"","");
            if (obj==null){
                return EasyResult.error(500,"申请信息不存在！！！");
            }

            List<JSONObject> list=CommHandleUtil.getInstance().getApplicationVesionInfo(applicationId,schema,query);
            if(CommonUtil.listIsNotNull(list)){
                    JSONObject json=list.get(list.size()-1);
                    JSONObject object=json.getJSONObject("objJson");
                    if (object!=null){
                        obj.put("numberType", object.getString("numberType"));
                        obj.put("groupName", object.getString("groupName"));
                        obj.put("numberSource", object.getString("numberSource"));
                        obj.put("phoneNumber", object.getString("phoneNumber"));
                    }
            }

            jsonObject.put("info",obj);
            //审核进度
            jsonObject.put("flowList", CommHandleUtil.getInstance().getFlowList(applicationId,schema,query));
            //具体流程
            jsonObject.put("approvalList", CommHandleUtil.getInstance().getApprovalList(applicationId,schema,query, Constants.GROUP_AUDIT_TYPE_1));
            return EasyResult.ok(jsonObject);
        } catch (Exception e) {
            logger.info("查询详情异常");
            return EasyResult.fail("查询详情异常");
        }
    }


    /**
     * 号码操作申请-多个号码
     * @return
     */
    @WebControl(name = "queryApplyDetail2", type = Types.RECORD)
    public JSONObject queryApplyDetail2() {
        String applicationId = param.getString("applicationId");
        if (StringUtils.isBlank(applicationId)){
            return EasyResult.error(500,"申请id不能为空");
        }
        JSONObject jsonObject = new JSONObject();
        EasyQuery query = getQuery();
        try {
            UserModel userModel = UserUtil.getUser(this.request);
            String schema=userModel.getSchemaName();
            //基础信息   //涉及号码  //操作信息
            JSONObject obj= CommHandleUtil.getInstance().getApplicationInfo(applicationId,schema,query,"","");
            if (obj==null){
                return EasyResult.error(500,"申请信息不存在！！！");
            }
            jsonObject.put("info",obj);

            //审核进度
            jsonObject.put("flowList", CommHandleUtil.getInstance().getFlowList(applicationId,schema,query));
            //具体流程
            jsonObject.put("approvalList", CommHandleUtil.getInstance().getApprovalList(applicationId,schema,query, Constants.GROUP_AUDIT_TYPE_1));
            return EasyResult.ok(jsonObject);
        } catch (Exception e) {
            logger.info("查询详情异常");
            return EasyResult.fail("查询详情异常");
        }
    }

    //获取多个号码列表
    @WebControl(name = "queryApplyPhoneDetail", type = Types.RECORD)
    public JSONObject queryApplyPhoneDetail() {
        String applicationId = param.getString("applicationId");
        if (StringUtils.isBlank(applicationId)){
            return EasyResult.error(500,"申请id不能为空");
        }
        JSONObject jsonObject = new JSONObject();
        EasyQuery query = getQuery();
        try {
            UserModel userModel = UserUtil.getUser(this.request);
            String schema=userModel.getSchemaName();
            //基础信息   //涉及号码  //操作信息
            JSONObject obj= CommHandleUtil.getInstance().getApplicationInfo(applicationId,schema,query,"","");
            if (obj==null){
                return EasyResult.error(500,"申请信息不存在！！！");
            }

            JSONObject phoneList=CommHandleUtil.getInstance().getApplicationVesionPageInfo(applicationId,schema,query,
                    param.getInteger("pageIndex"),param.getInteger("pageSize") );

            return EasyResult.ok(phoneList);
        } catch (Exception e) {
            logger.info("查询详情异常");
            return EasyResult.fail("查询详情异常");
        }
    }

    /**
     * 欢迎语申请-单个号码
     * @return
     */
    @WebControl(name = "queryWelcomeDetail", type = Types.RECORD)
    public JSONObject queryWelcomeDetail() {
        String applicationId = param.getString("applicationId");
        if (StringUtils.isBlank(applicationId)){
            return EasyResult.error(500,"申请id不能为空");
        }
        JSONObject jsonObject = new JSONObject();
        EasyQuery query = getQuery();
        try {
            UserModel userModel = UserUtil.getUser(this.request);
            String schema=userModel.getSchemaName();
            //基础信息   //涉及号码  //操作信息
            JSONObject obj= CommHandleUtil.getInstance().getApplicationInfo(applicationId,schema,query,"","");
            if (obj==null){
                return EasyResult.error(500,"申请信息不存在！！！");
            }
            String phoneId = "";
            List<JSONObject> list=CommHandleUtil.getInstance().getApplicationVesionInfo(applicationId,schema,query);
            if(CommonUtil.listIsNotNull(list)){
                JSONObject json=list.get(list.size()-1);
                JSONObject object=json.getJSONObject("objJson");
                if (object!=null){
                    obj.put("filePath",object.getString("filePath"));
                    phoneId = obj.getString("phoneId");
                }
            }

            if (StringUtils.isNotBlank(phoneId)) {
                //查询号码对应信息
                JSONObject phoneObj = CommHandleUtil.getInstance().getFilePhoneRecode(phoneId, schema, query,"1");
                if (phoneObj != null) {
                    obj.put("numberType", phoneObj.getString("numberType"));
                    obj.put("groupName", phoneObj.getString("groupName"));
                    obj.put("numberSource", phoneObj.getString("numberSource"));
                    obj.put("phoneNumber", phoneObj.getString("phoneNumber"));
                    obj.put("orgFilePath", phoneObj.getString("filePath"));
                }
            }

            jsonObject.put("info",obj);
            //审核进度
            jsonObject.put("flowList", CommHandleUtil.getInstance().getFlowList(applicationId,schema,query));
            //具体流程
            jsonObject.put("approvalList", CommHandleUtil.getInstance().getApprovalList(applicationId,schema,query, Constants.GROUP_AUDIT_TYPE_3));
            return EasyResult.ok(jsonObject);
        } catch (Exception e) {
            logger.info("查询详情异常");
            return EasyResult.fail("查询详情异常");
        }
    }

    /**
     * 工作时间申请-单个号码
     * @return
     */
    @WebControl(name = "queryApplyWorkDetail", type = Types.RECORD)
    public JSONObject queryApplyWorkDetail() {

        String applicationId = param.getString("applicationId");
        if (StringUtils.isBlank(applicationId)){
            return EasyResult.error(500,"申请id不能为空");
        }
        JSONObject jsonObject = new JSONObject();
        EasyQuery query = getQuery();
        try {
            UserModel userModel = UserUtil.getUser(this.request);
            String schema=userModel.getSchemaName();
            //基础信息   //涉及号码  //操作信息
            JSONObject obj= CommHandleUtil.getInstance().getApplicationInfo(applicationId,schema,query,"","");
            if (obj==null){
                return EasyResult.error(500,"申请信息不存在！！！");
            }
            String phoneId = "";
            List<JSONObject> list=CommHandleUtil.getInstance().getApplicationVesionInfo(applicationId,schema,query);
            if(CommonUtil.listIsNotNull(list)){
                JSONObject json=list.get(list.size()-1);
                JSONObject object=json.getJSONObject("objJson");
                if (object!=null){
                    obj.put("numberType", object.getString("numberType"));
                    obj.put("groupName", object.getString("groupName"));
                    obj.put("numberSource", object.getString("numberSource"));
                    obj.put("phoneNumber", object.getString("phoneNumber"));
                    phoneId = obj.getString("phoneId");
                }
            }
            if (StringUtils.isNotBlank(phoneId)) {
                //查询号码对应信息
                JSONObject phoneObj = CommHandleUtil.getInstance().getWorkTimePhoneRecode(phoneId, schema, query);
                if (phoneObj != null) {
                    obj.put("skipHoliday", phoneObj.getString("skipHoliday"));
                    obj.put("workDays", CommHandleUtil.getInstance().parseWeekdays(phoneObj.getString("workDays")));
                    obj.put("workTime1Start", phoneObj.getString("workTime1Start"));
                    obj.put("workTime1End", phoneObj.getString("workTime1End"));
                    obj.put("workTime2Start", phoneObj.getString("workTime2Start"));
                    obj.put("workTime2End", phoneObj.getString("workTime2End"));
                    obj.put("workTime3Start", phoneObj.getString("workTime3Start"));
                    obj.put("workTime3End", phoneObj.getString("workTime3End"));
                    obj.put("blacklistStatus", phoneObj.getString("workTime3End"));
                }
            }
            jsonObject.put("info",obj);
            //审核进度
            jsonObject.put("flowList", CommHandleUtil.getInstance().getFlowList(applicationId,schema,query));
            //具体流程
            jsonObject.put("approvalList", CommHandleUtil.getInstance().getApprovalList(applicationId,schema,query, Constants.GROUP_AUDIT_TYPE_3));
            return EasyResult.ok(jsonObject);
        } catch (Exception e) {
            logger.info("查询详情异常");
            return EasyResult.fail("查询详情异常");
        }
    }
    /**
     * 号码操作申请-多个号码
     * @return

    @WebControl(name = "queryApplyDetail2", type = Types.RECORD)
    public JSONObject queryApplyDetail2() {
        String applicationNo = param.getString("application_no");
        EasySQL sql1 = getApplyDetailSql(applicationNo);
        EasySQL sql2 = getPhoneSql(applicationNo);
        EasySQL sql3 = getApprovalStepSql(applicationNo);

        JSONObject result = new JSONObject();
        EasyQuery query = getQuery();
        try {
            EasyRow easyRow = query.queryForRow(sql1.getSQL(), sql1.getParams());
            result.put("application_detail", easyRow.toJSONObject());
            JSONObject phoneList = queryForPageList(sql2.getSQL(), sql2.getParams());
            result.put("phone_list", phoneList.get("data"));
            EasyRow auditFlow = query.queryForRow(sql3.getSQL(), sql3.getParams());
            result.put("approve_step", auditFlow.toJSONObject());
            return EasyResult.ok(result);
        } catch (SQLException e) {
            logger.info("查询详情异常");
            return EasyResult.fail("查询详情异常");
        }
    }



    /**
     * 工作时间申请-单个号码
     * @return
     *
    @WebControl(name = "queryApplyWorkDetail", type = Types.RECORD)
    public JSONObject queryApplyWorkDetail() {
        String applicationNo = param.getString("application_no");
        EasySQL sql1 = getApplyDetailSql(applicationNo);
        EasySQL sql2 = getPhoneSql(applicationNo);
        EasySQL sql3 = getApprovalStepSql(applicationNo);
        EasySQL sql4 = getApplyWorkSql(applicationNo);

        JSONObject result = new JSONObject();
        EasyQuery query = getQuery();
        try {
            EasyRow easyRow = query.queryForRow(sql1.getSQL(), sql1.getParams());
            if (easyRow != null) {
                result.put("application_detail", easyRow.toJSONObject());
            }
            JSONObject phoneList = queryForPageList(sql2.getSQL(), sql2.getParams());
            result.put("phone_list", phoneList.get("data"));
            EasyRow auditFlow = query.queryForRow(sql3.getSQL(), sql3.getParams());
            if (auditFlow != null) {
                result.put("approve_step", auditFlow.toJSONObject());
            }
            EasyRow workDetail = query.queryForRow(sql4.getSQL(), sql4.getParams());
            if (workDetail != null) {
                result.put("work_detail", workDetail.toJSONObject());
            }

            return EasyResult.ok(result);
        } catch (SQLException e) {
            logger.info("查询详情异常");
            return EasyResult.fail("查询详情异常");
        }
    } */

    /**
     * 查询工作信息
     * @param applicationNo
     * @return
     */
    private EasySQL getApplyWorkSql(String applicationNo) {
        /**
         * SELECT t.id,
         *        t.work_time1_start,
         *        t.work_time1_end,
         *        t.work_time2_start,
         *        t.work_time2_end,
         *        t.work_time3_start,
         *        t.work_time3_end,
         *        t.work_days,
         *        t.skip_holiday
         * FROM YCMAIN_ZSJ.zsj_work_time t
         *          JOIN YCMAIN_ZSJ.zsj_application a ON a.id = t.application_id
         * WHERE a.application_no = 'APP202406230006'
         */
        EasySQL sql = new EasySQL();
        sql.append("SELECT t.id,");
        sql.append("    t.work_time1_start,");
        sql.append("    t.work_time1_end,");
        sql.append("    t.work_time2_start,");
        sql.append("    t.work_time2_end,");
        sql.append("    t.work_time3_start,");
        sql.append("    t.work_time3_end,");
        sql.append("    t.work_days,");
        sql.append("    t.skip_holiday");
        sql.append("FROM");
        sql.append(this.getTableName("zsj_work_time") + " t ");
        sql.append("JOIN");
        sql.append(this.getTableName("zsj_application"));
        sql.append("a ON a.id = t.application_id");
        sql.append(applicationNo,"WHERE a.application_no = ?");
        logger.info("查询工作信息:" + sql.getFullSql());
        return sql;
    }

    /**
     * 查询具体审核流程
     * @param applicationNo
     * @return
     */
    private EasySQL getApprovalStepSql(String applicationNo) {
        /**
         * SELECT
         *     fi.step_name,
         *     fi.approver_name,
         *     fi.approval_status,
         *     fi.approval_desc ,
         *     fi.deal_unit
         * FROM zsj_approval_step fi
         * JOIN zsj_application a ON a.id = fi.application_id
         * WHERE a.application_no = 'AP20250623001';
         * order by fi.create_time asc
         */
        // 查审核流程信息
        EasySQL sql = new EasySQL();
        sql.append("SELECT");
        sql.append("    fi.application_id,");
        sql.append("    fi.step_name,");
        sql.append("    fi.approver_name,");
        sql.append("    fi.approval_status,");
        sql.append("    fi.approval_desc,");
        sql.append("    fi.create_time,");
        sql.append("    fi.deal_unit");
        sql.append("FROM");
        sql.append(this.getTableName("zsj_approval_step"));
        sql.append("fi");
        sql.append("JOIN");
        sql.append(this.getTableName("zsj_application"));
        sql.append("a ON a.id = fi.application_id");
        sql.append(applicationNo,"WHERE a.application_no = ?");
        sql.append("order by fi.create_time asc");
        logger.info("查询具体审核流程：" + sql.getFullSql());
        return sql;
    }

    /**
     * 查询申请详情
     * @param applicationNo
     * @return
     */
    private EasySQL getApplyDetailSql(String applicationNo) {
        /**
         * SELECT
         *     a.id AS application_id,
         *     a.application_no,
         *     a.business_type,
         *     a.phone_id,
         *     a.phone_number AS application_phone_number,
         *     a.applicant_id,
         *     a.applicant_name,
         *     a.application_desc,
         *     a.flow_status,
         *     a.close_status,
         *     a.application_time,
         *     a.update_time
         * FROM zsj_application a
         * WHERE a.application_no = 'AP20250623001';
         */
        // 申请表详情
        EasySQL sql = new EasySQL();
        sql.append("SELECT");
        sql.append("    a.id AS application_id,");
        sql.append("    a.application_no,");
        sql.append("    a.business_type,");
        sql.append("    a.phone_id,");
        sql.append("    a.phone_number AS application_phone_number,");
        sql.append("    a.applicant_id,");
        sql.append("    a.applicant_name,");
        sql.append("    a.application_desc,");
        sql.append("    a.flow_status,");
        sql.append("    a.close_status,");
        sql.append("    a.application_time,");
        sql.append("    a.update_time");
        sql.append("FROM");
        sql.append(this.getTableName("zsj_application") + " a");
        sql.append(applicationNo,"WHERE a.application_no = ?");
        logger.info("查询申请表详情：" + sql.getFullSql());
        return sql;
    }

    /**
     * 查询号码和欢迎语
     * @return
     */
    private EasySQL getPhoneSql(String applicationNo) {
        /**
         * SELECT
         *     pn.phone_number AS phone_number_info,
         *     pn.group_id,
         *     pn.group_name AS phone_group_name,
         *     pn.number_type,
         *     pn.number_source,
         *     af.file_path
         * FROM zsj_phone_number pn
         * JOIN zsj_application a ON a.id = pn.application_id
         * JOIN zsj_audio_file af ON af.phone_id = a.phone_id
         * WHERE a.application_no = 'AP20250623001';
         */
        // 申请表对应的号码
        EasySQL sql = new EasySQL();
        sql.append("SELECT");
        sql.append("    pn.phone_number AS phone_number_info,");
        sql.append("    pn.group_id,");
        sql.append("    pn.group_name AS phone_group_name,");
        sql.append("    pn.number_type,");
        sql.append("    pn.number_source,");
        sql.append("    af.file_path");
        sql.append("FROM " + this.getTableName("zsj_phone_number") + " pn");
        sql.append("JOIN " + this.getTableName("zsj_application") + " a ON a.id = pn.application_id");
        sql.append("JOIN " + this.getTableName("zsj_audio_file") + " af ON af.phone_id = a.phone_id");
        sql.append(applicationNo,"WHERE a.application_no = ?");
        logger.info("查询号码和欢迎语：" + sql.getFullSql());
        return sql;
    }



}
