{"info": {"name": "角色管理API", "version": "1.0.0", "description": "角色删除和角色用户编辑接口文档"}, "item": [{"name": "删除角色", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/servlet/role?action=actionForRoleDel", "host": ["{{baseUrl}}"], "path": ["servlet", "role"], "query": [{"key": "action", "value": "actionForRoleDel", "description": "接口方法名"}]}, "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"string\"\n}", "options": {"raw": {"language": "json"}}}, "description": "删除指定角色，删除前会检查角色中是否存在用户"}, "response": [{"name": "删除成功", "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"code\": 200,\n  \"message\": \"操作成功\",\n  \"data\": null\n}", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/servlet/role?action=actionForRoleDel", "host": ["{{baseUrl}}"], "path": ["servlet", "role"], "query": [{"key": "action", "value": "actionForRoleDel"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"role123\"\n}"}}}, {"name": "删除失败-角色中存在用户", "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": false,\n  \"code\": 500,\n  \"message\": \"该角色中存在用户，无法删除\",\n  \"data\": null\n}", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/servlet/role?action=actionForRoleDel", "host": ["{{baseUrl}}"], "path": ["servlet", "role"], "query": [{"key": "action", "value": "actionForRoleDel"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"role123\"\n}"}}}, {"name": "删除失败-参数缺失", "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": false,\n  \"code\": 500,\n  \"message\": \"缺少必填参数\",\n  \"data\": null\n}", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/servlet/role?action=actionForRoleDel", "host": ["{{baseUrl}}"], "path": ["servlet", "role"], "query": [{"key": "action", "value": "actionForRoleDel"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"\"\n}"}}}]}, {"name": "编辑角色用户", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/servlet/role?action=actionForEditUser", "host": ["{{baseUrl}}"], "path": ["servlet", "role"], "query": [{"key": "action", "value": "actionForEditUser", "description": "接口方法名"}]}, "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"string\",\n  \"userIds\": \"string\"\n}", "options": {"raw": {"language": "json"}}}, "description": "编辑角色下的用户，先删除角色下所有用户，再添加新的用户关系。userIds为逗号分隔的用户ID字符串，可为空表示清空角色下所有用户"}, "response": [{"name": "编辑成功", "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"code\": 200,\n  \"message\": \"操作成功\",\n  \"data\": null\n}", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/servlet/role?action=actionForEditUser", "host": ["{{baseUrl}}"], "path": ["servlet", "role"], "query": [{"key": "action", "value": "actionForEditUser"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"role123\",\n  \"userIds\": \"user1,user2,user3\"\n}"}}}, {"name": "编辑成功-清空用户", "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"code\": 200,\n  \"message\": \"操作成功\",\n  \"data\": null\n}", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/servlet/role?action=actionForEditUser", "host": ["{{baseUrl}}"], "path": ["servlet", "role"], "query": [{"key": "action", "value": "actionForEditUser"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"role123\",\n  \"userIds\": \"\"\n}"}}}, {"name": "编辑失败-参数缺失", "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": false,\n  \"code\": 500,\n  \"message\": \"缺少必填参数\",\n  \"data\": null\n}", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/servlet/role?action=actionForEditUser", "host": ["{{baseUrl}}"], "path": ["servlet", "role"], "query": [{"key": "action", "value": "actionForEditUser"}]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"\",\n  \"userIds\": \"user1,user2\"\n}"}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string", "description": "API基础URL"}]}